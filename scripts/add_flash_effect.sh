#!/bin/bash

# Script to add flash effect to all level pages

# List of level files
LEVEL_FILES=$(find /mnt/D_Drive/VS\ CODE/GIT/Asthra-TechDOS/techdos/app/levels/level-* -name "page.tsx" | grep -v "level-1" | grep -v "level-2")

for file in $LEVEL_FILES; do
  echo "Processing $file"
  
  # 1. Add flashState state variable
  sed -i 's/const \[submitLoading,setSubmitLoading\]=useState(false);\n  const \[isCompleted, setIsCompleted\]/const \[submitLoading,setSubmitLoading\]=useState(false);\n  const \[flashState, setFlashState\] = useState<'\''correct'\'' | '\''incorrect'\'' | null>(null);\n  const \[isCompleted, setIsCompleted\]/g' "$file"
  
  # 2. Add useEffect for flash state reset
  sed -i '/return () => clearInterval(timer);/,/}, \[team, timerStatus\]);/c\      return () => clearInterval(timer);\n    }\n  }, [team, timerStatus]);\n\n  \/\/ Add useEffect to reset flash state after animation\n  useEffect(() => {\n    if (flashState) {\n      const timer = setTimeout(() => {\n        setFlashState(null);\n      }, 800);\n      return () => clearTimeout(timer);\n    }\n  }, [flashState]);' "$file"
  
  # 3. Add flash state trigger in handleAnswer
  sed -i '/const isCorrect = answer === currentQuestion.correct;/,/setSubmitLoading(true);/c\    const isCorrect = answer === currentQuestion.correct;\n\n     if(submitLoading){\n      return;\n    }\n\n    setSubmitLoading(true);\n    \n    \/\/ Trigger flash effect for visual feedback\n    setFlashState(isCorrect ? '\''correct'\'' : '\''incorrect'\'');' "$file"
  
  # 4. Add FlashEffect component
  sed -i '/setIsCompleted(true);/,/};/c\      setIsCompleted(true);\n    } catch (error) {\n      console.error('\''Error completing level:'\'', error);\n      toast.error("Failed to save progress. Please try again.");\n    }\n  };\n\n  \/\/ Create a Flash Effect Component\n  const FlashEffect = () => {\n    if (!flashState) return null;\n    \n    return (\n      <div \n        className={`fixed inset-0 z-50 pointer-events-none animate-flash ${\n          flashState === '\''correct'\'' \n            ? '\''bg-green-500\/30'\'' \n            : '\''bg-red-500\/30'\''\n        }`} \n      \/>\n    );\n  };' "$file"
  
  # 5. Add FlashEffect to UI
  sed -i 's/<div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">\n      {\/\* Header \*\/}/<div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">\n      {\/\* Flash Effect \*\/}\n      <FlashEffect \/>\n      \n      {\/\* Header \*\/}/g' "$file"
  
  echo "Completed processing $file"
done

echo "All files have been updated"
